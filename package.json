{"name": "airekruitpro-interview", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "next build", "start": "NODE_ENV=production node server.js", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@types/express": "^5.0.3", "express": "^5.1.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "socket.io": "^4.8.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}