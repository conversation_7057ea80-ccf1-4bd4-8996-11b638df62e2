// Socket.io event types

export interface ClientToServerEvents {
  user_joined: (data: { 
    username: string; 
    threadId: string; 
    jobDescription: string; 
  }) => void;
  
  send_message: (data: { 
    message: string; 
    threadId: string; 
    timestamp: number; 
  }) => void;
  
  send_voice_message: (data: { 
    audioData: string; 
    duration: number; 
    threadId: string; 
    timestamp: number; 
  }) => void;
  
  typing_start: (data: { threadId: string }) => void;
  typing_stop: (data: { threadId: string }) => void;
  user_disconnect: (data: { threadId: string }) => void;
}

export interface ServerToClientEvents {
  message_received: (data: {
    id: string;
    message: string;
    username: string;
    threadId: string;
    timestamp: number;
    type: 'text';
    status: 'delivered';
  }) => void;
  
  voice_message_received: (data: {
    id: string;
    audioData: string;
    duration: number;
    username: string;
    threadId: string;
    timestamp: number;
    type: 'voice';
    status: 'delivered';
  }) => void;
  
  user_joined: (data: { 
    username: string; 
    onlineCount: number; 
  }) => void;
  
  user_left: (data: { 
    username: string; 
    onlineCount: number; 
  }) => void;
  
  typing_indicator: (data: { 
    username: string; 
    isTyping: boolean; 
  }) => void;
  
  interview_complete: (data: { 
    finalMessage: string; 
    completionTime: number; 
  }) => void;
  
  connection_error: (data: { 
    error: string; 
    shouldRetry: boolean; 
  }) => void;
  
  server_shutdown: (data: { 
    message: string; 
  }) => void;
}

export interface InterServerEvents {
  // For future scaling with multiple server instances
}

export interface SocketData {
  username: string;
  threadId: string;
}
