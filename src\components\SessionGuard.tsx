'use client';

import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';

interface SessionGuardProps {
  children: React.ReactNode;
  threadId: string;
  onSessionEnd?: () => void;
}

export default function SessionGuard({ children, threadId, onSessionEnd }: SessionGuardProps) {
  const router = useRouter();
  const [showWarning, setShowWarning] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [isVisible, setIsVisible] = useState(true);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Constants
  const IDLE_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  const WARNING_DURATION = 5000; // 5 seconds

  // Update last activity time
  const updateActivity = () => {
    lastActivityRef.current = Date.now();
  };

  // Handle page visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
      
      if (!document.hidden) {
        updateActivity();
        // Reset idle timeout when page becomes visible
        resetIdleTimeout();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  // Handle beforeunload (tab close/refresh)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      const message = 'Are you sure you want to leave? Your interview session will be lost.';
      e.preventDefault();
      e.returnValue = message;
      return message;
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, []);

  // Track user activity
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
      resetIdleTimeout();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, []);

  // Reset idle timeout
  const resetIdleTimeout = () => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }

    idleTimeoutRef.current = setTimeout(() => {
      showIdleWarning();
    }, IDLE_TIMEOUT);
  };

  // Show idle warning
  const showIdleWarning = () => {
    setShowWarning(true);
    setCountdown(5);

    // Start countdown
    countdownRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          handleSessionTimeout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Auto-close warning after duration
    warningTimeoutRef.current = setTimeout(() => {
      handleSessionTimeout();
    }, WARNING_DURATION);
  };

  // Handle session timeout
  const handleSessionTimeout = () => {
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }

    setShowWarning(false);
    
    // Store completion data
    const completionData = {
      finalMessage: 'Session ended due to inactivity.',
      completionTime: Date.now(),
      sessionSummary: {
        duration: Date.now() - (Date.now() - IDLE_TIMEOUT),
        messageCount: 0,
        voiceMessageCount: 0
      }
    };
    
    sessionStorage.setItem('interviewCompletionData', JSON.stringify(completionData));
    
    // Call session end callback
    if (onSessionEnd) {
      onSessionEnd();
    }
    
    // Navigate to completion page
    router.push('/complete');
  };

  // Continue session
  const continueSession = () => {
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }

    setShowWarning(false);
    updateActivity();
    resetIdleTimeout();
  };

  // Initialize idle timeout on mount
  useEffect(() => {
    resetIdleTimeout();
    
    return () => {
      if (idleTimeoutRef.current) {
        clearTimeout(idleTimeoutRef.current);
      }
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      {children}
      
      {/* Idle Warning Modal */}
      {showWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Session Timeout Warning
              </h3>
              
              <p className="text-gray-600 mb-4">
                Your interview session has been inactive for 30 minutes. 
                The session will end automatically in:
              </p>
              
              <div className="text-3xl font-bold text-red-600 mb-6">
                {countdown}
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={continueSession}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  Continue Session
                </button>
                
                <button
                  onClick={handleSessionTimeout}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                  End Session
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Visibility indicator */}
      {!isVisible && (
        <div className="fixed top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium z-40">
          Tab inactive
        </div>
      )}
    </>
  );
}
