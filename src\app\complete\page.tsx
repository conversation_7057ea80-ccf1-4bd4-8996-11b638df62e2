'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CheckCircleIcon, ClockIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { CompletionData } from '@/types/interview';

export default function CompletePage() {
  const router = useRouter();
  const [completionData, setCompletionData] = useState<CompletionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if there's completion data in sessionStorage
    const storedData = sessionStorage.getItem('interviewCompletionData');
    
    if (!storedData) {
      // Redirect to home if no completion data
      router.push('/');
      return;
    }

    try {
      const parsedData = JSON.parse(storedData) as CompletionData;
      setCompletionData(parsedData);
      setIsLoading(false);
    } catch (error) {
      console.error('Error parsing completion data:', error);
      router.push('/');
    }
  }, [router]);

  const handleStartNewInterview = () => {
    // Clear all session data
    sessionStorage.removeItem('interviewFormData');
    sessionStorage.removeItem('interviewCompletionData');
    
    // Navigate to home page
    router.push('/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading completion data...</p>
        </div>
      </div>
    );
  }

  if (!completionData) {
    return null; // Will redirect to home
  }

  const formatDuration = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
        <div className="text-center mb-8">
          <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Interview Complete!</h1>
          <p className="text-gray-600">Thank you for participating in the AI interview.</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Session Summary</h2>
          
          {completionData.sessionSummary && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <ClockIcon className="w-5 h-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Duration</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {formatDuration(completionData.sessionSummary.duration)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <ChatBubbleLeftRightIcon className="w-5 h-5 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Messages</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {completionData.sessionSummary.messageCount}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  <span className="text-sm text-gray-600">Voice Messages</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {completionData.sessionSummary.voiceMessageCount}
                </span>
              </div>
            </div>
          )}
        </div>

        {completionData.finalMessage && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Final Message</h3>
            <p className="text-sm text-blue-800">{completionData.finalMessage}</p>
          </div>
        )}

        <div className="space-y-3">
          <button
            onClick={handleStartNewInterview}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Start New Interview
          </button>
          
          <button
            onClick={() => window.close()}
            className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            Close Window
          </button>
        </div>

        <div className="mt-6 text-center text-xs text-gray-500">
          <p>Your interview data has been processed securely.</p>
        </div>
      </div>
    </div>
  );
}
