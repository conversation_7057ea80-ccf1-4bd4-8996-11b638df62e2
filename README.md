# Solara - AI-Powered Interview Chat Application

A comprehensive real-time interview platform built with Next.js 15, Socket.io, and advanced voice processing capabilities.

## 🚀 Features

### Core Functionality
- **Real-time Communication**: WebSocket-based messaging with automatic reconnection
- **Voice Processing**: 35-second voice recording with visual countdown and playback
- **AI Integration**: Seamless connection to Node-RED AI services
- **Session Management**: Browser tab/window close detection with confirmation dialogs
- **Performance Monitoring**: Built-in performance dashboard and metrics tracking

### Technical Highlights
- **Next.js 15**: Latest React framework with App Router
- **Socket.io v4+**: Real-time bidirectional communication
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Modern, responsive UI design
- **Web Audio API**: Advanced voice recording and playback
- **Error Boundaries**: Comprehensive error handling and recovery

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+
- npm or yarn
- Modern web browser with WebRTC support

### Installation
```bash
# Install dependencies
npm install

# Start the development server
npm run dev
```

### Environment Configuration
```env
# Node-RED API Configuration
NODE_RED_API_URL=http://127.0.0.1:1880/api
NODE_RED_API_KEY=your-api-key-here

# Application Configuration
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
NEXT_PUBLIC_APP_ENV=development
PORT=3000
SESSION_SECRET=your-session-secret-here

# Feature Limits
MAX_VOICE_DURATION=35000
MAX_MESSAGE_LENGTH=500
```

## 🎯 Usage Guide

### For Candidates
1. **Start Interview**: Fill out the interview form with your details
2. **Chat Interface**: Engage in real-time conversation with the AI interviewer
3. **Voice Messages**: Record and send voice messages (max 35 seconds)
4. **Session Management**: Your session is automatically saved and restored

### For Developers (Development Mode)
1. **Performance Dashboard**: Access via development tools in chat interface
2. **Test Suite**: Run comprehensive tests to validate functionality
3. **Error Monitoring**: Built-in error boundaries with detailed logging

## 📊 Implementation Status

✅ **Phase 1**: Express Server with Socket.io integration
✅ **Phase 2**: Interview Form with validation and navigation
✅ **Phase 3**: Basic Chat Interface with real-time messaging
✅ **Phase 4**: Voice Recording with 35-second countdown
✅ **Phase 5**: AI API Integration with error handling
✅ **Phase 6**: Session Management with tab detection
✅ **Phase 7**: Real-time Features (typing indicators, presence)
✅ **Phase 8**: Testing and Performance Optimization

## 🧪 Testing

### Built-in Test Suite
- Socket.io connectivity validation
- API endpoint functionality testing
- Browser capability detection
- Performance benchmarks
- Memory usage monitoring

Access the test suite via the development tools in the chat interface.

## 🔒 Security Features

- Input validation and sanitization
- Rate limiting (10 messages/minute per user)
- Session timeout (30 minutes inactivity)
- CORS configuration for Socket.io
- Error boundary protection

## 🚀 Production Deployment

```bash
# Build the application
npm run build

# Start production server
npm start
```

---

**Solara** - Revolutionizing AI-powered interviews with real-time communication and advanced voice processing.
