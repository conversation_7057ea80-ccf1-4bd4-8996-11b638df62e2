// Message and user types for the chat interface

export interface MessageData {
  id: string;
  message: string;
  username: string;
  threadId: string;
  timestamp: number;
  type: 'text' | 'voice' | 'system';
  status: 'sent' | 'delivered' | 'failed';
}

export interface VoiceMessageData {
  id: string;
  audioData: string; // base64 encoded audio
  duration: number; // in milliseconds
  username: string;
  threadId: string;
  timestamp: number;
  type: 'voice';
  status: 'sent' | 'delivered' | 'failed';
}

export interface User {
  id: string;
  username: string;
  socketId: string;
  isOnline: boolean;
  joinedAt: Date;
}

export interface TypingIndicator {
  username: string;
  isTyping: boolean;
  threadId: string;
}

export interface ChatState {
  messages: (MessageData | VoiceMessageData)[];
  users: User[];
  currentUser: string | null;
  threadId: string | null;
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: string[];
}

export type MessageType = MessageData | VoiceMessageData;
