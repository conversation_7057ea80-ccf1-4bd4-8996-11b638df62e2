'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { SOCKET_EVENTS } from '@/utils/socketEvents';
import { MessageData, VoiceMessageData } from '@/types/chat';
import { ClientToServerEvents, ServerToClientEvents } from '@/types/socket';
import { performanceMonitor, measureAsync, debounce } from '@/utils/performance';

interface UseSocketProps {
  username: string;
  threadId: string;
  jobDescription: string;
}

interface UseSocketReturn {
  socket: Socket<ServerToClientEvents, ClientToServerEvents> | null;
  isConnected: boolean;
  onlineCount: number;
  messages: (MessageData | VoiceMessageData)[];
  typingUsers: string[];
  sendMessage: (message: string) => void;
  sendVoiceMessage: (audioData: string, duration: number) => void;
  startTyping: () => void;
  stopTyping: () => void;
  disconnect: () => void;
}

export const useSocket = ({ username, threadId, jobDescription }: UseSocketProps): UseSocketReturn => {
  const socketRef = useRef<Socket<ServerToClientEvents, ClientToServerEvents> | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineCount, setOnlineCount] = useState(0);
  const [messages, setMessages] = useState<(MessageData | VoiceMessageData)[]>([]);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const connectionStartTime = useRef<number>(0);
  const reconnectAttempts = useRef<number>(0);
  const maxReconnectAttempts = 5;

  // Debounced typing functions for performance
  const debouncedStartTyping = useCallback(
    debounce(() => {
      if (socketRef.current && isConnected) {
        socketRef.current.emit(SOCKET_EVENTS.TYPING_START, { threadId });
      }
    }, 300),
    [isConnected, threadId]
  );

  const debouncedStopTyping = useCallback(
    debounce(() => {
      if (socketRef.current && isConnected) {
        socketRef.current.emit(SOCKET_EVENTS.TYPING_STOP, { threadId });
      }
    }, 1000),
    [isConnected, threadId]
  );

  useEffect(() => {
    // Initialize socket connection
    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';
    connectionStartTime.current = performance.now();

    socketRef.current = io(socketUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: maxReconnectAttempts,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000
    });

    const socket = socketRef.current;

    // Connection event handlers
    socket.on(SOCKET_EVENTS.CONNECT, () => {
      console.log('Connected to server:', socket.id);
      const connectionTime = performance.now() - connectionStartTime.current;
      performanceMonitor.recordConnectionTime(connectionTime);

      setIsConnected(true);
      reconnectAttempts.current = 0;

      // Join the interview session
      socket.emit(SOCKET_EVENTS.USER_JOINED, {
        username,
        threadId,
        jobDescription
      });
    });

    socket.on(SOCKET_EVENTS.DISCONNECT, (reason) => {
      console.log('Disconnected from server:', reason);
      setIsConnected(false);

      // Track disconnection reasons
      if (reason === 'io server disconnect') {
        console.warn('Server disconnected the client');
      } else if (reason === 'transport close') {
        console.warn('Connection lost due to transport close');
      }
    });

    socket.on(SOCKET_EVENTS.CONNECT_ERROR, (error) => {
      console.error('Connection error:', error);
      setIsConnected(false);
      reconnectAttempts.current++;

      if (reconnectAttempts.current >= maxReconnectAttempts) {
        console.error('Max reconnection attempts reached');
      }
    });

    socket.on(SOCKET_EVENTS.RECONNECT, (attemptNumber) => {
      console.log(`Reconnected after ${attemptNumber} attempts`);
      reconnectAttempts.current = 0;
    });

    socket.on(SOCKET_EVENTS.RECONNECT_ERROR, (error) => {
      console.error('Reconnection error:', error);
    });

    socket.on(SOCKET_EVENTS.RECONNECT_FAILED, () => {
      console.error('Failed to reconnect after maximum attempts');
    });

    // Message event handlers with performance monitoring
    socket.on(SOCKET_EVENTS.MESSAGE_RECEIVED, (data) => {
      performanceMonitor.startTimer('message_receive');
      console.log('Message received:', data);

      setMessages(prev => {
        const newMessages = [...prev, data];
        // Limit message history to prevent memory issues
        if (newMessages.length > 1000) {
          return newMessages.slice(-500); // Keep last 500 messages
        }
        return newMessages;
      });

      performanceMonitor.endTimer('message_receive');
      performanceMonitor.recordMemoryUsage();
    });

    socket.on(SOCKET_EVENTS.VOICE_MESSAGE_RECEIVED, (data) => {
      performanceMonitor.startTimer('voice_message_receive');
      console.log('Voice message received:', data);

      setMessages(prev => {
        const newMessages = [...prev, data];
        // Limit message history to prevent memory issues
        if (newMessages.length > 1000) {
          return newMessages.slice(-500); // Keep last 500 messages
        }
        return newMessages;
      });

      performanceMonitor.endTimer('voice_message_receive');
      performanceMonitor.recordMemoryUsage();
    });

    // User presence event handlers
    socket.on(SOCKET_EVENTS.USER_JOINED_RESPONSE, (data) => {
      console.log('User joined:', data);
      setOnlineCount(data.onlineCount);
    });

    socket.on(SOCKET_EVENTS.USER_LEFT, (data) => {
      console.log('User left:', data);
      setOnlineCount(data.onlineCount);
    });

    // Typing indicator handlers
    socket.on(SOCKET_EVENTS.TYPING_INDICATOR, (data) => {
      if (data.isTyping) {
        setTypingUsers(prev => {
          if (!prev.includes(data.username)) {
            return [...prev, data.username];
          }
          return prev;
        });
      } else {
        setTypingUsers(prev => prev.filter(user => user !== data.username));
      }
    });

    // Interview completion handler
    socket.on(SOCKET_EVENTS.INTERVIEW_COMPLETE, (data) => {
      console.log('Interview completed:', data);
      
      // Store completion data
      const completionData = {
        finalMessage: data.finalMessage,
        completionTime: data.completionTime,
        sessionSummary: {
          duration: Date.now() - (Date.now() - data.completionTime),
          messageCount: messages.length,
          voiceMessageCount: messages.filter(m => m.type === 'voice').length
        }
      };
      
      sessionStorage.setItem('interviewCompletionData', JSON.stringify(completionData));
      
      // Navigate to completion page after a short delay
      setTimeout(() => {
        window.location.href = '/complete';
      }, 2000);
    });

    // Error handlers
    socket.on(SOCKET_EVENTS.CONNECTION_ERROR, (data) => {
      console.error('Socket connection error:', data);
      if (data.shouldRetry) {
        setTimeout(() => {
          socket.connect();
        }, 5000);
      }
    });

    socket.on(SOCKET_EVENTS.SERVER_SHUTDOWN, (data) => {
      console.warn('Server shutdown:', data.message);
      alert('Server is shutting down. Please save your work.');
    });

    // Cleanup function
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      if (socket) {
        socket.emit(SOCKET_EVENTS.USER_DISCONNECT, { threadId });
        socket.disconnect();
      }

      // Clean up performance monitoring
      performanceMonitor.cleanup();
    };
  }, [username, threadId, jobDescription]);

  // Periodic performance monitoring cleanup
  useEffect(() => {
    const interval = setInterval(() => {
      performanceMonitor.cleanup();
      performanceMonitor.recordMemoryUsage();
    }, 60000); // Every minute

    return () => clearInterval(interval);
  }, []);

  const sendMessage = useCallback(async (message: string) => {
    if (!socketRef.current || !isConnected || !message.trim()) {
      return;
    }

    const trimmedMessage = message.trim();

    try {
      // Send user message immediately with performance tracking
      performanceMonitor.startTimer('message_send');

      socketRef.current.emit(SOCKET_EVENTS.SEND_MESSAGE, {
        message: trimmedMessage,
        threadId,
        timestamp: Date.now()
      });

      // Get AI response with performance monitoring
      const aiResponse = await measureAsync('ai_chat_request', async () => {
        const response = await fetch('/api/agent_chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: trimmedMessage,
            threadId,
            messageType: 'text',
            username
          }),
          signal: AbortSignal.timeout(30000) // 30 second timeout
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
      });

      performanceMonitor.endTimer('message_send');

      if (aiResponse.success && aiResponse.response) {
        // Send AI response through socket with realistic delay
        const delay = Math.min(1000 + (aiResponse.response.length * 20), 3000); // Dynamic delay based on response length

        setTimeout(() => {
          if (socketRef.current) {
            socketRef.current.emit(SOCKET_EVENTS.SEND_MESSAGE, {
              message: aiResponse.response,
              threadId,
              timestamp: Date.now()
            });
          }
        }, delay);

        // Check if interview should be completed
        if (aiResponse.shouldComplete) {
          setTimeout(() => {
            if (socketRef.current) {
              socketRef.current.emit(SOCKET_EVENTS.INTERVIEW_COMPLETE, {
                finalMessage: 'Thank you for your time! The interview has been completed.',
                completionTime: Date.now()
              });
            }
          }, delay + 1000);
        }
      }
    } catch (error) {
      console.error('Failed to send message or get AI response:', error);
      performanceMonitor.endTimer('message_send');

      // Show error message to user
      if (socketRef.current) {
        socketRef.current.emit(SOCKET_EVENTS.SEND_MESSAGE, {
          message: 'Sorry, I encountered an error processing your message. Please try again.',
          threadId,
          timestamp: Date.now()
        });
      }
    }
  }, [isConnected, threadId, username]);

  const sendVoiceMessage = async (audioData: string, duration: number) => {
    if (socketRef.current && isConnected) {
      // Send voice message immediately
      socketRef.current.emit(SOCKET_EVENTS.SEND_VOICE_MESSAGE, {
        audioData,
        duration,
        threadId,
        timestamp: Date.now()
      });

      // Get AI response for voice message
      try {
        const response = await fetch('/api/agent-voice', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            audioData,
            duration,
            threadId,
            username
          })
        });

        const data = await response.json();

        if (data.success && data.response) {
          // Send AI response through socket
          setTimeout(() => {
            if (socketRef.current) {
              socketRef.current.emit(SOCKET_EVENTS.SEND_MESSAGE, {
                message: data.response,
                threadId,
                timestamp: Date.now()
              });
            }
          }, 2000); // Longer delay for voice processing

          // Check if interview should be completed
          if (data.shouldComplete) {
            setTimeout(() => {
              if (socketRef.current) {
                socketRef.current.emit(SOCKET_EVENTS.INTERVIEW_COMPLETE, {
                  finalMessage: 'Thank you for your time! The interview has been completed.',
                  completionTime: Date.now()
                });
              }
            }, 3000);
          }
        }
      } catch (error) {
        console.error('Failed to get AI response for voice message:', error);
      }
    }
  };

  const startTyping = useCallback(() => {
    debouncedStartTyping();
  }, [debouncedStartTyping]);

  const stopTyping = useCallback(() => {
    debouncedStopTyping();
  }, [debouncedStopTyping]);

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.emit(SOCKET_EVENTS.USER_DISCONNECT, { threadId });
      socketRef.current.disconnect();
    }
  };

  return {
    socket: socketRef.current,
    isConnected,
    onlineCount,
    messages,
    typingUsers,
    sendMessage,
    sendVoiceMessage,
    startTyping,
    stopTyping,
    disconnect
  };
};
