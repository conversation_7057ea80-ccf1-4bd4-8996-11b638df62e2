'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { SOCKET_EVENTS } from '@/utils/socketEvents';
import { MessageData, VoiceMessageData } from '@/types/chat';
import { ClientToServerEvents, ServerToClientEvents } from '@/types/socket';

interface UseSocketProps {
  username: string;
  threadId: string;
  jobDescription: string;
}

interface UseSocketReturn {
  socket: Socket<ServerToClientEvents, ClientToServerEvents> | null;
  isConnected: boolean;
  onlineCount: number;
  messages: (MessageData | VoiceMessageData)[];
  typingUsers: string[];
  sendMessage: (message: string) => void;
  sendVoiceMessage: (audioData: string, duration: number) => void;
  startTyping: () => void;
  stopTyping: () => void;
  disconnect: () => void;
}

export const useSocket = ({ username, threadId, jobDescription }: UseSocketProps): UseSocketReturn => {
  const socketRef = useRef<Socket<ServerToClientEvents, ClientToServerEvents> | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineCount, setOnlineCount] = useState(0);
  const [messages, setMessages] = useState<(MessageData | VoiceMessageData)[]>([]);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Initialize socket connection
    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';
    
    socketRef.current = io(socketUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    const socket = socketRef.current;

    // Connection event handlers
    socket.on(SOCKET_EVENTS.CONNECT, () => {
      console.log('Connected to server:', socket.id);
      setIsConnected(true);
      
      // Join the interview session
      socket.emit(SOCKET_EVENTS.USER_JOINED, {
        username,
        threadId,
        jobDescription
      });
    });

    socket.on(SOCKET_EVENTS.DISCONNECT, () => {
      console.log('Disconnected from server');
      setIsConnected(false);
    });

    socket.on(SOCKET_EVENTS.CONNECT_ERROR, (error) => {
      console.error('Connection error:', error);
      setIsConnected(false);
    });

    // Message event handlers
    socket.on(SOCKET_EVENTS.MESSAGE_RECEIVED, (data) => {
      console.log('Message received:', data);
      setMessages(prev => [...prev, data]);
    });

    socket.on(SOCKET_EVENTS.VOICE_MESSAGE_RECEIVED, (data) => {
      console.log('Voice message received:', data);
      setMessages(prev => [...prev, data]);
    });

    // User presence event handlers
    socket.on(SOCKET_EVENTS.USER_JOINED_RESPONSE, (data) => {
      console.log('User joined:', data);
      setOnlineCount(data.onlineCount);
    });

    socket.on(SOCKET_EVENTS.USER_LEFT, (data) => {
      console.log('User left:', data);
      setOnlineCount(data.onlineCount);
    });

    // Typing indicator handlers
    socket.on(SOCKET_EVENTS.TYPING_INDICATOR, (data) => {
      if (data.isTyping) {
        setTypingUsers(prev => {
          if (!prev.includes(data.username)) {
            return [...prev, data.username];
          }
          return prev;
        });
      } else {
        setTypingUsers(prev => prev.filter(user => user !== data.username));
      }
    });

    // Interview completion handler
    socket.on(SOCKET_EVENTS.INTERVIEW_COMPLETE, (data) => {
      console.log('Interview completed:', data);
      
      // Store completion data
      const completionData = {
        finalMessage: data.finalMessage,
        completionTime: data.completionTime,
        sessionSummary: {
          duration: Date.now() - (Date.now() - data.completionTime),
          messageCount: messages.length,
          voiceMessageCount: messages.filter(m => m.type === 'voice').length
        }
      };
      
      sessionStorage.setItem('interviewCompletionData', JSON.stringify(completionData));
      
      // Navigate to completion page after a short delay
      setTimeout(() => {
        window.location.href = '/complete';
      }, 2000);
    });

    // Error handlers
    socket.on(SOCKET_EVENTS.CONNECTION_ERROR, (data) => {
      console.error('Socket connection error:', data);
      if (data.shouldRetry) {
        setTimeout(() => {
          socket.connect();
        }, 5000);
      }
    });

    socket.on(SOCKET_EVENTS.SERVER_SHUTDOWN, (data) => {
      console.warn('Server shutdown:', data.message);
      alert('Server is shutting down. Please save your work.');
    });

    // Cleanup function
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      if (socket) {
        socket.emit(SOCKET_EVENTS.USER_DISCONNECT, { threadId });
        socket.disconnect();
      }
    };
  }, [username, threadId, jobDescription, messages.length]);

  const sendMessage = async (message: string) => {
    if (socketRef.current && isConnected && message.trim()) {
      // Send user message immediately
      socketRef.current.emit(SOCKET_EVENTS.SEND_MESSAGE, {
        message: message.trim(),
        threadId,
        timestamp: Date.now()
      });

      // Get AI response
      try {
        const response = await fetch('/api/agent_chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: message.trim(),
            threadId,
            messageType: 'text',
            username
          })
        });

        const data = await response.json();

        if (data.success && data.response) {
          // Send AI response through socket
          setTimeout(() => {
            if (socketRef.current) {
              socketRef.current.emit(SOCKET_EVENTS.SEND_MESSAGE, {
                message: data.response,
                threadId,
                timestamp: Date.now()
              });
            }
          }, 1000); // Small delay to simulate AI thinking

          // Check if interview should be completed
          if (data.shouldComplete) {
            setTimeout(() => {
              if (socketRef.current) {
                socketRef.current.emit(SOCKET_EVENTS.INTERVIEW_COMPLETE, {
                  finalMessage: 'Thank you for your time! The interview has been completed.',
                  completionTime: Date.now()
                });
              }
            }, 2000);
          }
        }
      } catch (error) {
        console.error('Failed to get AI response:', error);
      }
    }
  };

  const sendVoiceMessage = async (audioData: string, duration: number) => {
    if (socketRef.current && isConnected) {
      // Send voice message immediately
      socketRef.current.emit(SOCKET_EVENTS.SEND_VOICE_MESSAGE, {
        audioData,
        duration,
        threadId,
        timestamp: Date.now()
      });

      // Get AI response for voice message
      try {
        const response = await fetch('/api/agent-voice', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            audioData,
            duration,
            threadId,
            username
          })
        });

        const data = await response.json();

        if (data.success && data.response) {
          // Send AI response through socket
          setTimeout(() => {
            if (socketRef.current) {
              socketRef.current.emit(SOCKET_EVENTS.SEND_MESSAGE, {
                message: data.response,
                threadId,
                timestamp: Date.now()
              });
            }
          }, 2000); // Longer delay for voice processing

          // Check if interview should be completed
          if (data.shouldComplete) {
            setTimeout(() => {
              if (socketRef.current) {
                socketRef.current.emit(SOCKET_EVENTS.INTERVIEW_COMPLETE, {
                  finalMessage: 'Thank you for your time! The interview has been completed.',
                  completionTime: Date.now()
                });
              }
            }, 3000);
          }
        }
      } catch (error) {
        console.error('Failed to get AI response for voice message:', error);
      }
    }
  };

  const startTyping = () => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(SOCKET_EVENTS.TYPING_START, { threadId });
    }
  };

  const stopTyping = () => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(SOCKET_EVENTS.TYPING_STOP, { threadId });
    }
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.emit(SOCKET_EVENTS.USER_DISCONNECT, { threadId });
      socketRef.current.disconnect();
    }
  };

  return {
    socket: socketRef.current,
    isConnected,
    onlineCount,
    messages,
    typingUsers,
    sendMessage,
    sendVoiceMessage,
    startTyping,
    stopTyping,
    disconnect
  };
};
