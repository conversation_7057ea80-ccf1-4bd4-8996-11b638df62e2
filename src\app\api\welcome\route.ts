import { NextRequest, NextResponse } from 'next/server';

interface WelcomeRequest {
  jobDescription: string;
  candidateDetails?: string;
  username: string;
}

interface WelcomeResponse {
  welcomeMessage: string;
  threadId: string;
  sessionId: string;
  success: boolean;
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: WelcomeRequest = await request.json();
    const { jobDescription, candidateDetails, username } = body;

    // Validate required fields
    if (!jobDescription || !username) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Job description and username are required' 
        },
        { status: 400 }
      );
    }

    // Generate unique IDs
    const threadId = `thread_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Prepare data for Node-RED API
    const nodeRedPayload = {
      jobDescription,
      candidateDetails: candidateDetails || '',
      username,
      threadId,
      sessionId,
      timestamp: new Date().toISOString()
    };

    // Call Node-RED API with retry logic
    let welcomeMessage = '';
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const nodeRedUrl = process.env.NODE_RED_API_URL || process.env.NODERED_URL;
        
        if (!nodeRedUrl) {
          throw new Error('Node-RED API URL not configured');
        }

        const response = await fetch(`${nodeRedUrl}/welcome`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.NODE_RED_API_KEY || ''}`,
          },
          body: JSON.stringify(nodeRedPayload),
          signal: AbortSignal.timeout(30000) // 30 second timeout
        });

        if (!response.ok) {
          throw new Error(`Node-RED API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        welcomeMessage = data.message || data.welcomeMessage || `Hello ${username}! Welcome to your AI interview session. I'm excited to learn more about your background and discuss the ${jobDescription.substring(0, 50)}${jobDescription.length > 50 ? '...' : ''} position with you. Let's get started!`;
        break;

      } catch (error) {
        retryCount++;
        console.error(`Node-RED API attempt ${retryCount} failed:`, error);
        
        if (retryCount >= maxRetries) {
          // Fallback welcome message
          welcomeMessage = `Hello ${username}! Welcome to your AI interview session. I'm excited to learn more about your background and discuss the ${jobDescription.substring(0, 50)}${jobDescription.length > 50 ? '...' : ''} position with you. Let's get started!`;
          console.warn('Using fallback welcome message after Node-RED API failures');
        } else {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
        }
      }
    }

    const response: WelcomeResponse = {
      welcomeMessage,
      threadId,
      sessionId,
      success: true
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Welcome API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to initialize interview session. Please try again.',
        welcomeMessage: 'Welcome to your AI interview session! Please try refreshing if you encounter any issues.',
        threadId: `fallback_${Date.now()}`,
        sessionId: `fallback_${Date.now()}`
      },
      { status: 500 }
    );
  }
}
