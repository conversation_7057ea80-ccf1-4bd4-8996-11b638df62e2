'use client';

import { useState, useRef, useEffect } from 'react';
import { PlayIcon, PauseIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline';
import { formatDuration } from '@/utils/audioUtils';

interface VoicePlayerProps {
  audioData: string;
  duration: number;
  className?: string;
  isOwnMessage?: boolean;
}

export default function VoicePlayer({ 
  audioData, 
  duration, 
  className = '', 
  isOwnMessage = false 
}: VoicePlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Cleanup audio on unmount
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
      }
    };
  }, []);

  const handlePlayPause = async () => {
    try {
      setError(null);
      
      if (isPlaying && audioRef.current) {
        audioRef.current.pause();
        return;
      }

      setIsLoading(true);

      // Create new audio element if it doesn't exist
      if (!audioRef.current) {
        const audio = new Audio(audioData);
        audioRef.current = audio;

        // Set up event listeners
        audio.addEventListener('loadstart', () => setIsLoading(true));
        audio.addEventListener('canplay', () => setIsLoading(false));
        audio.addEventListener('play', () => setIsPlaying(true));
        audio.addEventListener('pause', () => setIsPlaying(false));
        audio.addEventListener('ended', () => {
          setIsPlaying(false);
          setCurrentTime(0);
        });
        audio.addEventListener('timeupdate', () => {
          setCurrentTime(audio.currentTime * 1000); // Convert to milliseconds
        });
        audio.addEventListener('error', (e) => {
          console.error('Audio playback error:', e);
          setError('Failed to play audio');
          setIsLoading(false);
          setIsPlaying(false);
        });
      }

      await audioRef.current.play();
      setIsLoading(false);
    } catch (err) {
      console.error('Error playing audio:', err);
      setError('Failed to play audio');
      setIsLoading(false);
      setIsPlaying(false);
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !progressRef.current) return;

    const rect = progressRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = (percentage * duration) / 1000; // Convert to seconds

    audioRef.current.currentTime = Math.max(0, Math.min(newTime, duration / 1000));
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (error) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
          <SpeakerWaveIcon className="w-4 h-4 text-red-500" />
        </div>
        <span className="text-xs text-red-500">Audio error</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Play/Pause Button */}
      <button
        onClick={handlePlayPause}
        disabled={isLoading}
        className={`flex items-center justify-center w-8 h-8 rounded-full transition-colors ${
          isOwnMessage
            ? 'bg-blue-500 hover:bg-blue-400 text-white'
            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
        } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {isLoading ? (
          <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"></div>
        ) : isPlaying ? (
          <PauseIcon className="w-4 h-4" />
        ) : (
          <PlayIcon className="w-4 h-4" />
        )}
      </button>

      {/* Waveform/Progress Bar */}
      <div className="flex-1 min-w-0">
        <div
          ref={progressRef}
          onClick={handleProgressClick}
          className="relative h-6 cursor-pointer group"
        >
          {/* Background */}
          <div className={`absolute top-1/2 left-0 right-0 h-1 rounded-full transform -translate-y-1/2 ${
            isOwnMessage ? 'bg-blue-300' : 'bg-gray-300'
          }`}></div>
          
          {/* Progress */}
          <div
            className={`absolute top-1/2 left-0 h-1 rounded-full transform -translate-y-1/2 transition-all ${
              isOwnMessage ? 'bg-white' : 'bg-blue-500'
            }`}
            style={{ width: `${progressPercentage}%` }}
          ></div>
          
          {/* Progress Handle */}
          <div
            className={`absolute top-1/2 w-3 h-3 rounded-full transform -translate-y-1/2 -translate-x-1/2 transition-all opacity-0 group-hover:opacity-100 ${
              isOwnMessage ? 'bg-white' : 'bg-blue-500'
            }`}
            style={{ left: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Duration */}
      <div className="flex items-center space-x-1 text-xs">
        <SpeakerWaveIcon className="w-3 h-3" />
        <span>
          {isPlaying && currentTime > 0 
            ? `${formatDuration(currentTime)} / ${formatDuration(duration)}`
            : formatDuration(duration)
          }
        </span>
      </div>
    </div>
  );
}
