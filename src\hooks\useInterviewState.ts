'use client';

import { useState, useEffect, useCallback } from 'react';
import { InterviewSession, InterviewState } from '@/types/interview';

interface UseInterviewStateProps {
  username: string;
  jobDescription: string;
  candidateDetails?: string;
}

interface UseInterviewStateReturn extends InterviewState {
  initializeSession: () => Promise<boolean>;
  sendChatMessage: (message: string, threadId: string) => Promise<string | null>;
  sendVoiceMessage: (audioData: string, duration: number, threadId: string) => Promise<string | null>;
  completeInterview: () => void;
}

export const useInterviewState = ({ 
  username, 
  jobDescription, 
  candidateDetails 
}: UseInterviewStateProps): UseInterviewStateReturn => {
  const [state, setState] = useState<InterviewState>({
    session: null,
    isInitialized: false,
    isLoading: false,
    error: null
  });

  const initializeSession = useCallback(async (): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await fetch('/api/welcome', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          jobDescription,
          candidateDetails
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to initialize session');
      }

      const session: InterviewSession = {
        threadId: data.threadId,
        sessionId: data.sessionId,
        username,
        jobDescription,
        candidateDetails,
        startTime: new Date(),
        status: 'active',
        welcomeMessage: data.welcomeMessage
      };

      setState({
        session,
        isInitialized: true,
        isLoading: false,
        error: null
      });

      // Store session data in sessionStorage for persistence
      sessionStorage.setItem('interviewSession', JSON.stringify(session));

      return true;

    } catch (error) {
      console.error('Failed to initialize interview session:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to initialize session'
      }));
      return false;
    }
  }, [username, jobDescription, candidateDetails]);

  const sendChatMessage = useCallback(async (
    message: string, 
    threadId: string
  ): Promise<string | null> => {
    try {
      const response = await fetch('/api/agent_chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          threadId,
          messageType: 'text',
          username
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to send message');
      }

      // Check if interview should be completed
      if (data.shouldComplete) {
        completeInterview();
      }

      return data.response;

    } catch (error) {
      console.error('Failed to send chat message:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to send message'
      }));
      return null;
    }
  }, [username]);

  const sendVoiceMessage = useCallback(async (
    audioData: string, 
    duration: number, 
    threadId: string
  ): Promise<string | null> => {
    try {
      const response = await fetch('/api/agent-voice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audioData,
          duration,
          threadId,
          username
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to process voice message');
      }

      // Check if interview should be completed
      if (data.shouldComplete) {
        completeInterview();
      }

      return data.response;

    } catch (error) {
      console.error('Failed to send voice message:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to process voice message'
      }));
      return null;
    }
  }, [username]);

  const completeInterview = useCallback(() => {
    setState(prev => {
      if (prev.session) {
        const updatedSession: InterviewSession = {
          ...prev.session,
          endTime: new Date(),
          status: 'completed'
        };

        // Update session in storage
        sessionStorage.setItem('interviewSession', JSON.stringify(updatedSession));

        return {
          ...prev,
          session: updatedSession
        };
      }
      return prev;
    });
  }, []);

  // Load session from storage on mount
  useEffect(() => {
    const storedSession = sessionStorage.getItem('interviewSession');
    if (storedSession) {
      try {
        const session: InterviewSession = JSON.parse(storedSession);
        // Convert date strings back to Date objects
        session.startTime = new Date(session.startTime);
        if (session.endTime) {
          session.endTime = new Date(session.endTime);
        }

        setState({
          session,
          isInitialized: true,
          isLoading: false,
          error: null
        });
      } catch (error) {
        console.error('Failed to load session from storage:', error);
        sessionStorage.removeItem('interviewSession');
      }
    }
  }, []);

  return {
    ...state,
    initializeSession,
    sendChatMessage,
    sendVoiceMessage,
    completeInterview
  };
};
