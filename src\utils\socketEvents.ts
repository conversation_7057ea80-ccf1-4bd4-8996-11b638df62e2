// Socket.io event constants

export const SOCKET_EVENTS = {
  // Client to Server
  USER_JOINED: 'user_joined',
  SEND_MESSAGE: 'send_message',
  SEND_VOICE_MESSAGE: 'send_voice_message',
  TYPING_START: 'typing_start',
  TYPING_STOP: 'typing_stop',
  USER_DISCONNECT: 'user_disconnect',
  
  // Server to Client
  MESSAGE_RECEIVED: 'message_received',
  VOICE_MESSAGE_RECEIVED: 'voice_message_received',
  USER_JOINED_RESPONSE: 'user_joined',
  USER_LEFT: 'user_left',
  TYPING_INDICATOR: 'typing_indicator',
  INTERVIEW_COMPLETE: 'interview_complete',
  CONNECTION_ERROR: 'connection_error',
  SERVER_SHUTDOWN: 'server_shutdown',
  
  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  CONNECT_ERROR: 'connect_error',
  RECONNECT: 'reconnect',
  RECONNECT_ERROR: 'reconnect_error',
  RECONNECT_FAILED: 'reconnect_failed'
} as const;

export type SocketEventType = typeof SOCKET_EVENTS[keyof typeof SOCKET_EVENTS];
