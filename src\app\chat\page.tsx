'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { InterviewFormData } from '@/types/interview';
import ChatInterface from '@/components/ChatInterface';
import SessionGuard from '@/components/SessionGuard';
import { useSessionPersistence } from '@/hooks/useSessionPersistence';

export default function ChatPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<InterviewFormData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [threadId, setThreadId] = useState<string>('');
  const { saveSession, updateActivity } = useSessionPersistence();

  useEffect(() => {
    // Check if user has completed the interview form
    const storedData = sessionStorage.getItem('interviewFormData');

    if (!storedData) {
      // Redirect to home page if no form data found
      router.push('/');
      return;
    }

    try {
      const parsedData = JSON.parse(storedData) as InterviewFormData;
      setFormData(parsedData);

      // Generate a unique thread ID for this session
      const newThreadId = `thread_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      setThreadId(newThreadId);

      // Save session data for persistence
      saveSession({
        formData: parsedData,
        threadId: newThreadId,
        messages: [],
        startTime: Date.now(),
        lastActivity: Date.now()
      });

      setIsLoading(false);
    } catch (error) {
      console.error('Error parsing form data:', error);
      router.push('/');
    }
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading interview session...</p>
        </div>
      </div>
    );
  }

  if (!formData || !threadId) {
    return null; // Will redirect to home
  }

  const handleSessionEnd = () => {
    console.log('Session ended by SessionGuard');
  };

  return (
    <SessionGuard threadId={threadId} onSessionEnd={handleSessionEnd}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto h-screen flex flex-col">
          <div className="flex-1 bg-white rounded-lg shadow-sm border m-4 overflow-hidden">
            <ChatInterface
              username={formData.username}
              threadId={threadId}
              jobDescription={formData.jobDescription}
            />
          </div>
        </div>
      </div>
    </SessionGuard>
  );
}
