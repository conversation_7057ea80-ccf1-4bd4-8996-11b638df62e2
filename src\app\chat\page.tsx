'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { InterviewFormData } from '@/types/interview';

export default function ChatPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<InterviewFormData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user has completed the interview form
    const storedData = sessionStorage.getItem('interviewFormData');
    
    if (!storedData) {
      // Redirect to home page if no form data found
      router.push('/');
      return;
    }

    try {
      const parsedData = JSON.parse(storedData) as InterviewFormData;
      setFormData(parsedData);
      setIsLoading(false);
    } catch (error) {
      console.error('Error parsing form data:', error);
      router.push('/');
    }
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading interview session...</p>
        </div>
      </div>
    );
  }

  if (!formData) {
    return null; // Will redirect to home
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-4">
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="border-b p-4">
            <h1 className="text-xl font-semibold text-gray-900">
              Interview with {formData.username}
            </h1>
            <p className="text-sm text-gray-600 mt-1">
              Position: {formData.jobDescription.substring(0, 100)}
              {formData.jobDescription.length > 100 ? '...' : ''}
            </p>
          </div>
          
          <div className="p-4">
            <div className="text-center text-gray-500 py-8">
              <p>Chat interface will be implemented in Phase 3</p>
              <p className="text-sm mt-2">Form data loaded successfully!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
