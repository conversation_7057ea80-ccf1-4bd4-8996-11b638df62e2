import { NextRequest, NextResponse } from 'next/server';

interface JobDescriptionRequest {
  threadId: string;
}

interface JobDescriptionResponse {
  jobDescription: string;
  candidateDetails?: string;
  username: string;
  sessionInfo: {
    threadId: string;
    startTime: string;
    status: string;
  };
  success: boolean;
  error?: string;
}

// In-memory storage for demo purposes
// In production, this would be stored in a database
const sessionStorage = new Map<string, {
  jobDescription: string;
  candidateDetails?: string;
  username: string;
  startTime: string;
  status: string;
}>();

export async function POST(request: NextRequest) {
  try {
    const body: JobDescriptionRequest = await request.json();
    const { threadId } = body;

    // Validate required fields
    if (!threadId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Thread ID is required' 
        },
        { status: 400 }
      );
    }

    // Try to get session data from memory first
    const sessionData = sessionStorage.get(threadId);
    
    if (sessionData) {
      const response: JobDescriptionResponse = {
        jobDescription: sessionData.jobDescription,
        candidateDetails: sessionData.candidateDetails,
        username: sessionData.username,
        sessionInfo: {
          threadId,
          startTime: sessionData.startTime,
          status: sessionData.status
        },
        success: true
      };

      return NextResponse.json(response);
    }

    // If not in memory, try to fetch from Node-RED API
    let retryCount = 0;
    const maxRetries = 2;

    while (retryCount < maxRetries) {
      try {
        const nodeRedUrl = process.env.NODE_RED_API_URL || process.env.NODERED_URL;
        
        if (!nodeRedUrl) {
          throw new Error('Node-RED API URL not configured');
        }

        const response = await fetch(`${nodeRedUrl}/session/${threadId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.NODE_RED_API_KEY || ''}`,
          },
          signal: AbortSignal.timeout(10000) // 10 second timeout
        });

        if (!response.ok) {
          throw new Error(`Node-RED API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        const responseData: JobDescriptionResponse = {
          jobDescription: data.jobDescription || 'No job description available',
          candidateDetails: data.candidateDetails,
          username: data.username || 'Unknown User',
          sessionInfo: {
            threadId,
            startTime: data.startTime || new Date().toISOString(),
            status: data.status || 'active'
          },
          success: true
        };

        // Cache the data in memory
        sessionStorage.set(threadId, {
          jobDescription: responseData.jobDescription,
          candidateDetails: responseData.candidateDetails,
          username: responseData.username,
          startTime: responseData.sessionInfo.startTime,
          status: responseData.sessionInfo.status
        });

        return NextResponse.json(responseData);

      } catch (error) {
        retryCount++;
        console.error(`Node-RED session API attempt ${retryCount} failed:`, error);
        
        if (retryCount >= maxRetries) {
          // Return fallback data
          const fallbackResponse: JobDescriptionResponse = {
            jobDescription: 'Session information not available',
            username: 'User',
            sessionInfo: {
              threadId,
              startTime: new Date().toISOString(),
              status: 'active'
            },
            success: true
          };

          return NextResponse.json(fallbackResponse);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

  } catch (error) {
    console.error('Get job description API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve session information. Please try again.',
        jobDescription: 'Error retrieving job description',
        username: 'User',
        sessionInfo: {
          threadId: 'unknown',
          startTime: new Date().toISOString(),
          status: 'error'
        }
      },
      { status: 500 }
    );
  }
}

// Helper function to store session data (can be called from other APIs)
export function storeSessionData(
  threadId: string, 
  data: {
    jobDescription: string;
    candidateDetails?: string;
    username: string;
  }
) {
  sessionStorage.set(threadId, {
    ...data,
    startTime: new Date().toISOString(),
    status: 'active'
  });
}

// Helper function to update session status
export function updateSessionStatus(threadId: string, status: string) {
  const existing = sessionStorage.get(threadId);
  if (existing) {
    sessionStorage.set(threadId, {
      ...existing,
      status
    });
  }
}
