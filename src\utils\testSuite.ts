// Comprehensive test suite for Solara Interview Application

export interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
}

export interface TestSuite {
  name: string;
  tests: TestResult[];
  passed: number;
  failed: number;
  duration: number;
}

class SolaraTestRunner {
  private results: TestSuite[] = [];

  // Test Socket.io connectivity
  async testSocketConnection(): Promise<TestResult> {
    const startTime = performance.now();
    
    try {
      // Test if Socket.io is available
      if (typeof window === 'undefined') {
        throw new Error('Running in server environment');
      }

      const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';
      const response = await fetch(`${socketUrl}/socket.io/`);
      
      const duration = performance.now() - startTime;
      
      if (response.ok) {
        return {
          name: 'Socket.io Connection',
          passed: true,
          message: 'Socket.io server is accessible',
          duration
        };
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      return {
        name: 'Socket.io Connection',
        passed: false,
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: performance.now() - startTime
      };
    }
  }

  // Test API endpoints
  async testApiEndpoints(): Promise<TestResult[]> {
    const endpoints = [
      { path: '/api/welcome', method: 'POST' },
      { path: '/api/agent_chat', method: 'POST' },
      { path: '/api/agent-voice', method: 'POST' },
      { path: '/api/get-jobdescription', method: 'POST' }
    ];

    const results: TestResult[] = [];

    for (const endpoint of endpoints) {
      const startTime = performance.now();
      
      try {
        const testData = endpoint.path === '/api/welcome' 
          ? { username: 'Test User', jobDescription: 'Test Job', candidateDetails: 'Test Details' }
          : endpoint.path === '/api/agent_chat'
          ? { message: 'Hello', threadId: 'test-thread', messageType: 'text' }
          : endpoint.path === '/api/agent-voice'
          ? { audioData: 'data:audio/wav;base64,test', threadId: 'test-thread', duration: 1000 }
          : { threadId: 'test-thread' };

        const response = await fetch(endpoint.path, {
          method: endpoint.method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testData)
        });

        const duration = performance.now() - startTime;

        if (response.ok) {
          const data = await response.json();
          results.push({
            name: `API ${endpoint.path}`,
            passed: true,
            message: `Endpoint responding correctly (${response.status})`,
            duration
          });
        } else {
          results.push({
            name: `API ${endpoint.path}`,
            passed: false,
            message: `HTTP ${response.status}: ${response.statusText}`,
            duration
          });
        }
      } catch (error) {
        results.push({
          name: `API ${endpoint.path}`,
          passed: false,
          message: `Request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          duration: performance.now() - startTime
        });
      }
    }

    return results;
  }

  // Test browser capabilities
  async testBrowserCapabilities(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    const startTime = performance.now();

    // Test MediaRecorder API
    try {
      if (typeof MediaRecorder !== 'undefined') {
        results.push({
          name: 'MediaRecorder API',
          passed: true,
          message: 'Voice recording supported',
          duration: performance.now() - startTime
        });
      } else {
        throw new Error('MediaRecorder not available');
      }
    } catch (error) {
      results.push({
        name: 'MediaRecorder API',
        passed: false,
        message: 'Voice recording not supported',
        duration: performance.now() - startTime
      });
    }

    // Test getUserMedia API
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        results.push({
          name: 'getUserMedia API',
          passed: true,
          message: 'Microphone access supported',
          duration: performance.now() - startTime
        });
      } else {
        throw new Error('getUserMedia not available');
      }
    } catch (error) {
      results.push({
        name: 'getUserMedia API',
        passed: false,
        message: 'Microphone access not supported',
        duration: performance.now() - startTime
      });
    }

    // Test Audio API
    try {
      const audio = new Audio();
      if (audio.canPlayType) {
        results.push({
          name: 'Audio Playback',
          passed: true,
          message: 'Audio playback supported',
          duration: performance.now() - startTime
        });
      } else {
        throw new Error('Audio playback limited');
      }
    } catch (error) {
      results.push({
        name: 'Audio Playback',
        passed: false,
        message: 'Audio playback not supported',
        duration: performance.now() - startTime
      });
    }

    // Test localStorage
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      results.push({
        name: 'Local Storage',
        passed: true,
        message: 'Session persistence supported',
        duration: performance.now() - startTime
      });
    } catch (error) {
      results.push({
        name: 'Local Storage',
        passed: false,
        message: 'Session persistence not supported',
        duration: performance.now() - startTime
      });
    }

    return results;
  }

  // Test performance benchmarks
  async testPerformance(): Promise<TestResult[]> {
    const results: TestResult[] = [];

    // Test memory usage
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const memoryUsageMB = memory.usedJSHeapSize / 1024 / 1024;
      
      results.push({
        name: 'Memory Usage',
        passed: memoryUsageMB < 100,
        message: `Current usage: ${memoryUsageMB.toFixed(2)}MB`,
        duration: 0
      });
    }

    // Test render performance
    const startTime = performance.now();
    await new Promise(resolve => requestAnimationFrame(resolve));
    const renderTime = performance.now() - startTime;
    
    results.push({
      name: 'Render Performance',
      passed: renderTime < 16.67, // 60fps threshold
      message: `Frame time: ${renderTime.toFixed(2)}ms`,
      duration: renderTime
    });

    return results;
  }

  // Run all tests
  async runAllTests(): Promise<TestSuite[]> {
    this.results = [];
    
    const suites = [
      {
        name: 'Connectivity',
        tests: [await this.testSocketConnection()]
      },
      {
        name: 'API Endpoints',
        tests: await this.testApiEndpoints()
      },
      {
        name: 'Browser Capabilities',
        tests: await this.testBrowserCapabilities()
      },
      {
        name: 'Performance',
        tests: await this.testPerformance()
      }
    ];

    // Calculate suite statistics
    this.results = suites.map(suite => ({
      ...suite,
      passed: suite.tests.filter(t => t.passed).length,
      failed: suite.tests.filter(t => !t.passed).length,
      duration: suite.tests.reduce((sum, t) => sum + t.duration, 0)
    }));

    return this.results;
  }

  // Get test summary
  getSummary(): {
    totalTests: number;
    totalPassed: number;
    totalFailed: number;
    totalDuration: number;
    overallPassed: boolean;
  } {
    const totalTests = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failed, 0);
    const totalDuration = this.results.reduce((sum, suite) => sum + suite.duration, 0);

    return {
      totalTests,
      totalPassed,
      totalFailed,
      totalDuration,
      overallPassed: totalFailed === 0
    };
  }

  // Export results
  exportResults(): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      results: this.results,
      summary: this.getSummary()
    }, null, 2);
  }
}

export const testRunner = new SolaraTestRunner();
