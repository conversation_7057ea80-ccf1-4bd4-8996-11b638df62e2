'use client';

import { useEffect, useState } from 'react';
import { 
  MicrophoneIcon, 
  StopIcon, 
  PlayIcon, 
  PauseIcon,
  XMarkIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';
import { useVoiceCapture } from '@/hooks/useVoiceCapture';
import { AUDIO_CONFIG, formatDuration } from '@/utils/audioUtils';

interface VoiceRecorderProps {
  onSendVoice: (audioData: string, duration: number) => void;
  onCancel: () => void;
  disabled?: boolean;
}

export default function VoiceRecorder({ onSendVoice, onCancel, disabled = false }: VoiceRecorderProps) {
  const [showRecorder, setShowRecorder] = useState(false);
  const [recordedAudio, setRecordedAudio] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackAudio, setPlaybackAudio] = useState<HTMLAudioElement | null>(null);

  const {
    isRecording,
    isPaused,
    recordingTime,
    hasPermission,
    error,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    cancelRecording,
    requestPermission
  } = useVoiceCapture();

  // Calculate remaining time
  const remainingTime = Math.max(0, AUDIO_CONFIG.MAX_DURATION - recordingTime);
  const isNearLimit = remainingTime <= 5000; // Last 5 seconds

  useEffect(() => {
    // Cleanup audio on unmount
    return () => {
      if (playbackAudio) {
        playbackAudio.pause();
        playbackAudio.src = '';
      }
    };
  }, [playbackAudio]);

  const handleStartRecording = async () => {
    if (!hasPermission) {
      const granted = await requestPermission();
      if (!granted) return;
    }
    
    setShowRecorder(true);
    setRecordedAudio(null);
    await startRecording();
  };

  const handleStopRecording = async () => {
    const audioData = await stopRecording();
    if (audioData) {
      setRecordedAudio(audioData);
    }
  };

  const handlePlayRecording = async () => {
    if (!recordedAudio) return;

    if (isPlaying && playbackAudio) {
      playbackAudio.pause();
      setIsPlaying(false);
      return;
    }

    try {
      const audio = new Audio(recordedAudio);
      setPlaybackAudio(audio);

      audio.onplay = () => setIsPlaying(true);
      audio.onpause = () => setIsPlaying(false);
      audio.onended = () => {
        setIsPlaying(false);
        setPlaybackAudio(null);
      };
      audio.onerror = () => {
        console.error('Error playing recorded audio');
        setIsPlaying(false);
        setPlaybackAudio(null);
      };

      await audio.play();
    } catch (error) {
      console.error('Error playing recording:', error);
    }
  };

  const handleSendRecording = () => {
    if (recordedAudio) {
      onSendVoice(recordedAudio, recordingTime);
      handleCancel();
    }
  };

  const handleCancel = () => {
    if (isRecording) {
      cancelRecording();
    }
    if (playbackAudio) {
      playbackAudio.pause();
      playbackAudio.src = '';
      setPlaybackAudio(null);
    }
    setShowRecorder(false);
    setRecordedAudio(null);
    setIsPlaying(false);
    onCancel();
  };

  if (!showRecorder) {
    return (
      <button
        type="button"
        onClick={handleStartRecording}
        disabled={disabled}
        className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        title="Record voice message"
      >
        <MicrophoneIcon className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            Voice Message
          </h3>
          <button
            onClick={handleCancel}
            className="p-1 text-gray-400 hover:text-gray-600 rounded-full"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Recording Status */}
        <div className="text-center mb-6">
          {isRecording ? (
            <div className="space-y-3">
              <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
                isNearLimit ? 'bg-red-100' : 'bg-blue-100'
              }`}>
                <div className={`w-4 h-4 rounded-full animate-pulse ${
                  isNearLimit ? 'bg-red-500' : 'bg-blue-500'
                }`}></div>
              </div>
              <div>
                <p className={`text-lg font-mono ${isNearLimit ? 'text-red-600' : 'text-blue-600'}`}>
                  {formatDuration(recordingTime)}
                </p>
                <p className="text-sm text-gray-500">
                  {formatDuration(remainingTime)} remaining
                </p>
              </div>
            </div>
          ) : recordedAudio ? (
            <div className="space-y-3">
              <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              </div>
              <div>
                <p className="text-lg font-mono text-green-600">
                  {formatDuration(recordingTime)}
                </p>
                <p className="text-sm text-gray-500">Recording complete</p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                <MicrophoneIcon className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-sm text-gray-500">Ready to record</p>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center space-x-4">
          {isRecording ? (
            <>
              {isPaused ? (
                <button
                  onClick={resumeRecording}
                  className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                  title="Resume recording"
                >
                  <PlayIcon className="w-6 h-6" />
                </button>
              ) : (
                <button
                  onClick={pauseRecording}
                  className="p-3 bg-yellow-600 text-white rounded-full hover:bg-yellow-700 transition-colors"
                  title="Pause recording"
                >
                  <PauseIcon className="w-6 h-6" />
                </button>
              )}
              
              <button
                onClick={handleStopRecording}
                className="p-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                title="Stop recording"
              >
                <StopIcon className="w-6 h-6" />
              </button>
            </>
          ) : recordedAudio ? (
            <>
              <button
                onClick={handlePlayRecording}
                className="p-3 bg-gray-600 text-white rounded-full hover:bg-gray-700 transition-colors"
                title={isPlaying ? "Pause playback" : "Play recording"}
              >
                {isPlaying ? <PauseIcon className="w-6 h-6" /> : <PlayIcon className="w-6 h-6" />}
              </button>
              
              <button
                onClick={() => {
                  setRecordedAudio(null);
                  handleStartRecording();
                }}
                className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                title="Record again"
              >
                <MicrophoneIcon className="w-6 h-6" />
              </button>
              
              <button
                onClick={handleSendRecording}
                className="p-3 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors"
                title="Send voice message"
              >
                <PaperAirplaneIcon className="w-6 h-6" />
              </button>
            </>
          ) : (
            <button
              onClick={handleStartRecording}
              disabled={!hasPermission}
              className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Start recording"
            >
              <MicrophoneIcon className="w-6 h-6" />
            </button>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-6 text-center text-xs text-gray-500">
          <p>Maximum recording time: {formatDuration(AUDIO_CONFIG.MAX_DURATION)}</p>
          {!hasPermission && (
            <p className="text-red-500 mt-1">Microphone permission required</p>
          )}
        </div>
      </div>
    </div>
  );
}
