'use client';

import { useState, useEffect } from 'react';

interface TypingIndicatorProps {
  typingUsers: string[];
}

export default function TypingIndicator({ typingUsers }: TypingIndicatorProps) {
  const [dots, setDots] = useState('');

  // Animate dots
  useEffect(() => {
    if (typingUsers.length === 0) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [typingUsers.length]);

  if (typingUsers.length === 0) {
    return null;
  }

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return `${typingUsers[0]} is typing`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0]} and ${typingUsers[1]} are typing`;
    } else {
      return `${typingUsers.length} people are typing`;
    }
  };

  return (
    <div className="flex justify-start mb-4 animate-fade-in">
      <div className="max-w-xs lg:max-w-md">
        <div className="bg-gray-100 px-4 py-3 rounded-lg shadow-sm">
          <div className="flex items-center space-x-3">
            {/* Enhanced typing animation */}
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
              <div
                className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                style={{ animationDelay: '0.1s' }}
              ></div>
              <div
                className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                style={{ animationDelay: '0.2s' }}
              ></div>
            </div>

            {/* Typing text with animated dots */}
            <div className="flex items-center">
              <span className="text-xs text-gray-600 font-medium">
                {getTypingText()}
              </span>
              <span className="text-xs text-gray-600 ml-1 w-4 text-left">
                {dots}
              </span>
            </div>
          </div>
        </div>

        {/* Small arrow pointing to the typing bubble */}
        <div className="ml-4 mt-1">
          <div className="w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-100"></div>
        </div>
      </div>
    </div>
  );
}
