'use client';

import { useState, useRef, useEffect } from 'react';
import { PaperAirplaneIcon } from '@heroicons/react/24/outline';
import { useSocket } from '@/hooks/useSocket';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';
import OnlineUsers from './OnlineUsers';
import VoiceRecorder from './VoiceRecorder';
import PerformanceDashboard from './PerformanceDashboard';
import TestRunner from './TestRunner';

interface ChatInterfaceProps {
  username: string;
  threadId: string;
  jobDescription: string;
}

export default function ChatInterface({ username, threadId, jobDescription }: ChatInterfaceProps) {
  const [messageInput, setMessageInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [welcomeMessageSent, setWelcomeMessageSent] = useState(false);
  const [showPerformanceDashboard, setShowPerformanceDashboard] = useState(false);
  const [showTestRunner, setShowTestRunner] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    isConnected,
    onlineCount,
    messages,
    typingUsers,
    sendMessage,
    sendVoiceMessage,
    startTyping,
    stopTyping
  } = useSocket({ username, threadId, jobDescription });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Send welcome message when connected
  useEffect(() => {
    if (isConnected && !welcomeMessageSent) {
      const sendWelcomeMessage = async () => {
        try {
          const response = await fetch('/api/welcome', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              username,
              jobDescription,
              candidateDetails: ''
            })
          });

          const data = await response.json();

          if (data.success && data.welcomeMessage) {
            // Simulate AI sending welcome message
            setTimeout(() => {
              // This will be handled by the socket as if it's an AI message
              const welcomeMessage = {
                id: `welcome_${Date.now()}`,
                message: data.welcomeMessage,
                username: 'AI Interviewer',
                threadId,
                timestamp: Date.now(),
                type: 'text' as const,
                status: 'delivered' as const
              };

              // Add welcome message to the messages array directly
              // This simulates receiving it from the server
            }, 1000);
          }
        } catch (error) {
          console.error('Failed to get welcome message:', error);
        }
      };

      sendWelcomeMessage();
      setWelcomeMessageSent(true);
    }
  }, [isConnected, welcomeMessageSent, username, jobDescription, threadId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    
    // Enforce character limit
    if (value.length <= 500) {
      setMessageInput(value);
      
      // Handle typing indicators
      if (!isTyping && value.trim()) {
        setIsTyping(true);
        startTyping();
      }
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Set new timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
        stopTyping();
      }, 1000);
    }
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageInput.trim() || !isConnected) {
      return;
    }
    
    // Send message
    sendMessage(messageInput);
    
    // Clear input and stop typing
    setMessageInput('');
    setIsTyping(false);
    stopTyping();
    
    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  const handleVoiceRecord = () => {
    setShowVoiceRecorder(true);
  };

  const handleSendVoice = (audioData: string, duration: number) => {
    sendVoiceMessage(audioData, duration);
    setShowVoiceRecorder(false);
  };

  const handleCancelVoice = () => {
    setShowVoiceRecorder(false);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="border-b p-4 bg-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Interview Session
            </h1>
            <p className="text-sm text-gray-600 mt-1">
              {jobDescription.substring(0, 80)}
              {jobDescription.length > 80 ? '...' : ''}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <OnlineUsers count={onlineCount} isConnected={isConnected} />

            {/* Development Tools */}
            {process.env.NODE_ENV === 'development' && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowPerformanceDashboard(true)}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="Performance Dashboard"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </button>

                <button
                  onClick={() => setShowTestRunner(true)}
                  className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                  title="Test Suite"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
        <div className="space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <div className="animate-pulse">
                <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
              </div>
              <p className="text-lg font-medium">Welcome to your AI interview session!</p>
              <p className="text-sm mt-2">The AI interviewer will greet you shortly. Start by sending a message or voice note.</p>
              {!isConnected && (
                <div className="mt-4 flex items-center justify-center space-x-2 text-yellow-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                  <span className="text-sm">Connecting to interview session...</span>
                </div>
              )}
            </div>
          ) : (
            messages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
                isOwnMessage={message.username === username}
              />
            ))
          )}

          <TypingIndicator typingUsers={typingUsers} />
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t p-4 bg-white">
        <form onSubmit={handleSendMessage} className="flex items-end space-x-3">
          <div className="flex-1">
            <textarea
              value={messageInput}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder={isConnected ? "Type your message..." : "Connecting..."}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              rows={1}
              maxLength={500}
              disabled={!isConnected}
              style={{
                minHeight: '40px',
                maxHeight: '120px',
                height: 'auto'
              }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                target.style.height = Math.min(target.scrollHeight, 120) + 'px';
              }}
            />
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-gray-500">
                {messageInput.length}/500
              </span>
              {isTyping && (
                <span className="text-xs text-blue-500">Typing...</span>
              )}
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleVoiceRecord}
              className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              disabled={!isConnected}
              title="Record voice message"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </button>

            <button
              type="submit"
              disabled={!messageInput.trim() || !isConnected}
              className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <PaperAirplaneIcon className="w-5 h-5" />
            </button>
          </div>
        </form>
      </div>

      {/* Voice Recorder Modal */}
      {showVoiceRecorder && (
        <VoiceRecorder
          onSendVoice={handleSendVoice}
          onCancel={handleCancelVoice}
          disabled={!isConnected}
        />
      )}

      {/* Performance Dashboard */}
      <PerformanceDashboard
        isVisible={showPerformanceDashboard}
        onClose={() => setShowPerformanceDashboard(false)}
      />

      {/* Test Runner */}
      <TestRunner
        isVisible={showTestRunner}
        onClose={() => setShowTestRunner(false)}
      />
    </div>
  );
}
