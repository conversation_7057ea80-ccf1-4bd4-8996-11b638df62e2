const { createServer } = require('http');
const { Server } = require('socket.io');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const port = process.env.PORT || 3000;

// Initialize Next.js app
const app = next({ dev });
const handler = app.getRequestHandler();

// Session storage for active interviews
const activeSessions = new Map();
const userSockets = new Map();

app.prepare().then(() => {
  const httpServer = createServer(handler);
  
  // Initialize Socket.io with CORS configuration
  const io = new Server(httpServer, {
    cors: {
      origin: process.env.NODE_ENV === 'production' 
        ? process.env.NEXT_PUBLIC_SOCKET_URL 
        : "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true
    },
    transports: ['websocket', 'polling']
  });

  // Socket.io connection handling
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.id}`);

    // Handle user joining an interview session
    socket.on('user_joined', (data) => {
      const { username, threadId, jobDescription } = data;
      
      // Store user session data
      const sessionData = {
        socketId: socket.id,
        username,
        threadId,
        jobDescription,
        joinedAt: new Date(),
        isActive: true
      };
      
      activeSessions.set(threadId, sessionData);
      userSockets.set(socket.id, threadId);
      
      // Join the thread room
      socket.join(threadId);
      
      // Notify others in the room
      socket.to(threadId).emit('user_joined', {
        username,
        onlineCount: io.sockets.adapter.rooms.get(threadId)?.size || 1
      });
      
      console.log(`User ${username} joined thread ${threadId}`);
    });

    // Handle text message sending
    socket.on('send_message', (data) => {
      const { message, threadId, timestamp } = data;
      const session = activeSessions.get(threadId);
      
      if (session) {
        const messageData = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          message,
          username: session.username,
          threadId,
          timestamp,
          type: 'text',
          status: 'delivered'
        };
        
        // Broadcast message to all users in the thread
        io.to(threadId).emit('message_received', messageData);
        console.log(`Message sent in thread ${threadId}: ${message.substring(0, 50)}...`);
      }
    });

    // Handle voice message sending
    socket.on('send_voice_message', (data) => {
      const { audioData, duration, threadId, timestamp } = data;
      const session = activeSessions.get(threadId);
      
      if (session) {
        const voiceMessageData = {
          id: `voice_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          audioData,
          duration,
          username: session.username,
          threadId,
          timestamp,
          type: 'voice',
          status: 'delivered'
        };
        
        // Broadcast voice message to all users in the thread
        io.to(threadId).emit('voice_message_received', voiceMessageData);
        console.log(`Voice message sent in thread ${threadId}, duration: ${duration}ms`);
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      const { threadId } = data;
      const session = activeSessions.get(threadId);
      
      if (session) {
        socket.to(threadId).emit('typing_indicator', {
          username: session.username,
          isTyping: true
        });
      }
    });

    socket.on('typing_stop', (data) => {
      const { threadId } = data;
      const session = activeSessions.get(threadId);
      
      if (session) {
        socket.to(threadId).emit('typing_indicator', {
          username: session.username,
          isTyping: false
        });
      }
    });

    // Handle user disconnection
    socket.on('disconnect', () => {
      const threadId = userSockets.get(socket.id);
      
      if (threadId) {
        const session = activeSessions.get(threadId);
        
        if (session) {
          // Notify others in the room
          socket.to(threadId).emit('user_left', {
            username: session.username,
            onlineCount: Math.max(0, (io.sockets.adapter.rooms.get(threadId)?.size || 1) - 1)
          });
          
          console.log(`User ${session.username} disconnected from thread ${threadId}`);
        }
        
        // Clean up session data
        activeSessions.delete(threadId);
        userSockets.delete(socket.id);
      }
      
      console.log(`User disconnected: ${socket.id}`);
    });

    // Handle explicit user disconnect
    socket.on('user_disconnect', (data) => {
      const { threadId } = data;
      socket.leave(threadId);
      socket.disconnect();
    });
  });

  // Note: API routes will be handled by Next.js API routes instead

  // Graceful shutdown handling
  process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    
    // Notify all connected clients
    io.emit('server_shutdown', { message: 'Server is shutting down' });
    
    // Close server
    httpServer.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  });

  httpServer.listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://localhost:${port}`);
    console.log(`> Socket.io server running on port ${port}`);
  });
});
