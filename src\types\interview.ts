// Interview session types

export interface InterviewFormData {
  username: string;
  jobDescription: string;
  candidateDetails?: string;
}

export interface InterviewSession {
  threadId: string;
  sessionId: string;
  username: string;
  jobDescription: string;
  candidateDetails?: string;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'cancelled';
  welcomeMessage?: string;
}

export interface InterviewState {
  session: InterviewSession | null;
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface CompletionData {
  finalMessage: string;
  completionTime: number;
  sessionSummary?: {
    duration: number;
    messageCount: number;
    voiceMessageCount: number;
  };
}
