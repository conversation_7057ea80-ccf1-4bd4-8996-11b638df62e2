'use client';

import { useState } from 'react';
import { testRunner, TestSuite, TestResult } from '@/utils/testSuite';
import { PlayIcon, CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/outline';

interface TestRunnerProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function TestRunner({ isVisible, onClose }: TestRunnerProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestSuite[]>([]);
  const [summary, setSummary] = useState<any>(null);

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    setSummary(null);

    try {
      const testResults = await testRunner.runAllTests();
      setResults(testResults);
      setSummary(testRunner.getSummary());
    } catch (error) {
      console.error('Test execution failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const exportResults = () => {
    const data = testRunner.exportResults();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `solara-test-results-${new Date().toISOString()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getTestIcon = (test: TestResult) => {
    if (test.passed) {
      return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
    } else {
      return <XCircleIcon className="w-4 h-4 text-red-500" />;
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-2">
            <PlayIcon className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Solara Test Suite</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Controls */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={runTests}
              disabled={isRunning}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isRunning ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Running Tests...</span>
                </>
              ) : (
                <>
                  <PlayIcon className="w-4 h-4" />
                  <span>Run All Tests</span>
                </>
              )}
            </button>

            {results.length > 0 && (
              <button
                onClick={exportResults}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
              >
                Export Results
              </button>
            )}
          </div>

          {/* Summary */}
          {summary && (
            <div className={`rounded-lg p-4 mb-6 ${summary.overallPassed ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <div className="flex items-center space-x-2 mb-2">
                {summary.overallPassed ? (
                  <CheckCircleIcon className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircleIcon className="w-5 h-5 text-red-600" />
                )}
                <h3 className={`font-medium ${summary.overallPassed ? 'text-green-800' : 'text-red-800'}`}>
                  Test Summary
                </h3>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Tests:</span>
                  <span className="ml-2 font-medium">{summary.totalTests}</span>
                </div>
                <div>
                  <span className="text-gray-600">Passed:</span>
                  <span className="ml-2 font-medium text-green-600">{summary.totalPassed}</span>
                </div>
                <div>
                  <span className="text-gray-600">Failed:</span>
                  <span className="ml-2 font-medium text-red-600">{summary.totalFailed}</span>
                </div>
                <div>
                  <span className="text-gray-600">Duration:</span>
                  <span className="ml-2 font-medium">{summary.totalDuration.toFixed(2)}ms</span>
                </div>
              </div>
            </div>
          )}

          {/* Test Results */}
          {results.length > 0 && (
            <div className="space-y-6">
              {results.map((suite, suiteIndex) => (
                <div key={suiteIndex} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">{suite.name}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="flex items-center space-x-1">
                          <CheckCircleIcon className="w-4 h-4 text-green-500" />
                          <span>{suite.passed}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <XCircleIcon className="w-4 h-4 text-red-500" />
                          <span>{suite.failed}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <ClockIcon className="w-4 h-4" />
                          <span>{suite.duration.toFixed(2)}ms</span>
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="divide-y divide-gray-200">
                    {suite.tests.map((test, testIndex) => (
                      <div key={testIndex} className="px-4 py-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getTestIcon(test)}
                            <span className="font-medium text-gray-900">{test.name}</span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {test.duration.toFixed(2)}ms
                          </span>
                        </div>
                        <p className={`mt-1 text-sm ${test.passed ? 'text-green-600' : 'text-red-600'}`}>
                          {test.message}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Instructions */}
          {results.length === 0 && !isRunning && (
            <div className="text-center py-12">
              <PlayIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Test</h3>
              <p className="text-gray-600 mb-4">
                Run the comprehensive test suite to validate all Solara features and performance.
              </p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>• Socket.io connectivity</p>
                <p>• API endpoint functionality</p>
                <p>• Browser capability detection</p>
                <p>• Performance benchmarks</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
