import { NextRequest, NextResponse } from 'next/server';

interface VoiceRequest {
  audioData: string;
  threadId: string;
  duration: number;
  username?: string;
}

interface VoiceResponse {
  transcription: string;
  response: string;
  shouldComplete: boolean;
  confidence: number;
  success: boolean;
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: VoiceRequest = await request.json();
    const { audioData, threadId, duration, username } = body;

    // Validate required fields
    if (!audioData || !threadId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Audio data and thread ID are required' 
        },
        { status: 400 }
      );
    }

    // Validate audio duration
    const maxDuration = parseInt(process.env.MAX_VOICE_DURATION || '35000');
    if (duration > maxDuration) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Voice message too long. Maximum ${maxDuration/1000} seconds allowed.` 
        },
        { status: 400 }
      );
    }

    // Prepare data for Node-RED API
    const nodeRedPayload = {
      audioData,
      threadId,
      duration,
      username: username || 'User',
      timestamp: new Date().toISOString()
    };

    // Call Node-RED API with retry logic
    let transcription = '';
    let aiResponse = '';
    let shouldComplete = false;
    let confidence = 0.8;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const nodeRedUrl = process.env.NODE_RED_API_URL || process.env.NODERED_URL;
        
        if (!nodeRedUrl) {
          throw new Error('Node-RED API URL not configured');
        }

        const response = await fetch(`${nodeRedUrl}/voice`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.NODE_RED_API_KEY || ''}`,
          },
          body: JSON.stringify(nodeRedPayload),
          signal: AbortSignal.timeout(60000) // 60 second timeout for voice processing
        });

        if (!response.ok) {
          throw new Error(`Node-RED API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        transcription = data.transcription || data.text || '';
        aiResponse = data.response || data.message || '';
        shouldComplete = data.shouldComplete || data.complete || false;
        confidence = data.confidence || 0.8;
        
        if (!transcription && !aiResponse) {
          throw new Error('Empty response from Node-RED API');
        }
        
        break;

      } catch (error) {
        retryCount++;
        console.error(`Node-RED voice API attempt ${retryCount} failed:`, error);
        
        if (retryCount >= maxRetries) {
          // Fallback response for voice messages
          transcription = '[Voice message received but could not be transcribed]';
          aiResponse = "I received your voice message, but I'm having trouble processing it right now. Could you please try sending it as text instead, or try recording again?";
          confidence = 0.1;
          console.warn('Using fallback voice response after Node-RED API failures');
        } else {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
        }
      }
    }

    const response: VoiceResponse = {
      transcription,
      response: aiResponse,
      shouldComplete,
      confidence,
      success: true
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Agent voice API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process voice message. Please try again.',
        transcription: '[Error processing voice message]',
        response: 'I apologize, but I\'m having trouble processing your voice message right now. Could you please try again or send your message as text?',
        shouldComplete: false,
        confidence: 0.1
      },
      { status: 500 }
    );
  }
}
