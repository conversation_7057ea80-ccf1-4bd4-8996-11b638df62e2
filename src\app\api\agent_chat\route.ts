import { NextRequest, NextResponse } from 'next/server';

interface ChatRequest {
  message: string;
  threadId: string;
  messageType: 'text' | 'voice';
  audioData?: string;
  username?: string;
}

interface ChatResponse {
  response: string;
  shouldComplete: boolean;
  confidence: number;
  success: boolean;
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: ChatRequest = await request.json();
    const { message, threadId, messageType, audioData, username } = body;

    // Validate required fields
    if (!message || !threadId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Message and thread ID are required' 
        },
        { status: 400 }
      );
    }

    // Validate message length
    if (message.length > 500) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Message too long. Maximum 500 characters allowed.' 
        },
        { status: 400 }
      );
    }

    // Prepare data for Node-RED API
    const nodeRedPayload = {
      message,
      threadId,
      messageType,
      audioData: messageType === 'voice' ? audioData : undefined,
      username: username || 'User',
      timestamp: new Date().toISOString()
    };

    // Call Node-RED API with retry logic
    let aiResponse = '';
    let shouldComplete = false;
    let confidence = 0.8;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const nodeRedUrl = process.env.NODE_RED_API_URL || process.env.NODERED_URL;
        
        if (!nodeRedUrl) {
          throw new Error('Node-RED API URL not configured');
        }

        const response = await fetch(`${nodeRedUrl}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.NODE_RED_API_KEY || ''}`,
          },
          body: JSON.stringify(nodeRedPayload),
          signal: AbortSignal.timeout(30000) // 30 second timeout
        });

        if (!response.ok) {
          throw new Error(`Node-RED API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        aiResponse = data.response || data.message || '';
        shouldComplete = data.shouldComplete || data.complete || false;
        confidence = data.confidence || 0.8;
        
        if (!aiResponse) {
          throw new Error('Empty response from Node-RED API');
        }
        
        break;

      } catch (error) {
        retryCount++;
        console.error(`Node-RED API attempt ${retryCount} failed:`, error);
        
        if (retryCount >= maxRetries) {
          // Fallback AI response based on message content
          aiResponse = generateFallbackResponse(message, messageType);
          console.warn('Using fallback AI response after Node-RED API failures');
        } else {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
        }
      }
    }

    const response: ChatResponse = {
      response: aiResponse,
      shouldComplete,
      confidence,
      success: true
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Agent chat API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process message. Please try again.',
        response: 'I apologize, but I\'m having trouble processing your message right now. Could you please try again?',
        shouldComplete: false,
        confidence: 0.1
      },
      { status: 500 }
    );
  }
}

function generateFallbackResponse(message: string, messageType: 'text' | 'voice'): string {
  const lowerMessage = message.toLowerCase();
  
  // Simple keyword-based responses for fallback
  if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
    return "Hello! Thank you for that introduction. Could you tell me more about your relevant experience for this position?";
  }
  
  if (lowerMessage.includes('experience') || lowerMessage.includes('work')) {
    return "That's interesting experience! Can you elaborate on how that experience would apply to this role? What specific skills did you develop?";
  }
  
  if (lowerMessage.includes('skill') || lowerMessage.includes('ability')) {
    return "Those are valuable skills! Can you give me a specific example of how you've used these skills in a professional setting?";
  }
  
  if (lowerMessage.includes('challenge') || lowerMessage.includes('difficult')) {
    return "Thank you for sharing that challenge. How did you approach solving it, and what did you learn from that experience?";
  }
  
  if (lowerMessage.includes('question') || lowerMessage.includes('ask')) {
    return "That's a great question! Let me think about that. In the meantime, could you tell me about a project you're particularly proud of?";
  }
  
  if (lowerMessage.includes('thank') || lowerMessage.includes('bye')) {
    return "Thank you for your time today! It was great learning about your background. We'll be in touch soon regarding next steps.";
  }
  
  // Default responses
  const defaultResponses = [
    "That's very interesting! Could you elaborate on that point a bit more?",
    "Thank you for sharing that. Can you give me a specific example?",
    "I'd like to understand that better. Could you walk me through your thought process?",
    "That sounds like valuable experience. How do you think it applies to this role?",
    "Interesting perspective! What led you to that conclusion?",
    "Could you tell me more about your approach to that situation?"
  ];
  
  const responsePrefix = messageType === 'voice' 
    ? "Thank you for that voice message. " 
    : "";
    
  return responsePrefix + defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
}
