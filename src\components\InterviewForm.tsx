'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { UserIcon, BriefcaseIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { InterviewFormData } from '@/types/interview';

interface InterviewFormProps {
  onSubmit: (data: InterviewFormData) => void;
}

export default function InterviewForm({ onSubmit }: InterviewFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState<InterviewFormData>({
    username: '',
    jobDescription: '',
    candidateDetails: ''
  });
  const [errors, setErrors] = useState<Partial<InterviewFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Partial<InterviewFormData> = {};

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    } else if (formData.username.trim().length < 2) {
      newErrors.username = 'Username must be at least 2 characters';
    } else if (formData.username.trim().length > 50) {
      newErrors.username = 'Username must be less than 50 characters';
    }

    // Job description validation
    if (!formData.jobDescription.trim()) {
      newErrors.jobDescription = 'Job description is required';
    } else if (formData.jobDescription.trim().length < 10) {
      newErrors.jobDescription = 'Job description must be at least 10 characters';
    } else if (formData.jobDescription.trim().length > 500) {
      newErrors.jobDescription = 'Job description must be less than 500 characters';
    }

    // Candidate details validation (optional)
    if (formData.candidateDetails && formData.candidateDetails.trim().length > 1000) {
      newErrors.candidateDetails = 'Candidate details must be less than 1000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof InterviewFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Store form data in sessionStorage for persistence
      sessionStorage.setItem('interviewFormData', JSON.stringify(formData));
      
      // Call the onSubmit callback
      onSubmit(formData);
      
      // Navigate to chat page
      router.push('/chat');
    } catch (error) {
      console.error('Error submitting form:', error);
      setErrors({ username: 'An error occurred. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Solara</h1>
          <p className="text-gray-600">AI-Powered Interview Assistant</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Username Field */}
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
              <UserIcon className="w-4 h-4 inline mr-2" />
              Username *
            </label>
            <input
              type="text"
              id="username"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.username ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter your name"
              maxLength={50}
              disabled={isSubmitting}
            />
            {errors.username && (
              <p className="mt-1 text-sm text-red-600">{errors.username}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {formData.username.length}/50 characters
            </p>
          </div>

          {/* Job Description Field */}
          <div>
            <label htmlFor="jobDescription" className="block text-sm font-medium text-gray-700 mb-2">
              <BriefcaseIcon className="w-4 h-4 inline mr-2" />
              Job Description *
            </label>
            <textarea
              id="jobDescription"
              value={formData.jobDescription}
              onChange={(e) => handleInputChange('jobDescription', e.target.value)}
              rows={4}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                errors.jobDescription ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Describe the position you're applying for..."
              maxLength={500}
              disabled={isSubmitting}
            />
            {errors.jobDescription && (
              <p className="mt-1 text-sm text-red-600">{errors.jobDescription}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {formData.jobDescription.length}/500 characters
            </p>
          </div>

          {/* Candidate Details Field */}
          <div>
            <label htmlFor="candidateDetails" className="block text-sm font-medium text-gray-700 mb-2">
              <DocumentTextIcon className="w-4 h-4 inline mr-2" />
              Additional Details (Optional)
            </label>
            <textarea
              id="candidateDetails"
              value={formData.candidateDetails}
              onChange={(e) => handleInputChange('candidateDetails', e.target.value)}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                errors.candidateDetails ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Any additional information about yourself..."
              maxLength={1000}
              disabled={isSubmitting}
            />
            {errors.candidateDetails && (
              <p className="mt-1 text-sm text-red-600">{errors.candidateDetails}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {formData.candidateDetails?.length || 0}/1000 characters
            </p>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Starting Interview...
              </div>
            ) : (
              'Start Interview'
            )}
          </button>
        </form>

        <div className="mt-6 text-center text-xs text-gray-500">
          <p>Your interview session will be secure and private.</p>
        </div>
      </div>
    </div>
  );
}
