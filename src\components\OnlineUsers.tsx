'use client';

import { useState, useEffect } from 'react';
import { UsersIcon, WifiIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface OnlineUsersProps {
  count: number;
  isConnected: boolean;
}

export default function OnlineUsers({ count, isConnected }: OnlineUsersProps) {
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connecting');
  const [lastSeen, setLastSeen] = useState<Date | null>(null);

  useEffect(() => {
    if (isConnected) {
      setConnectionStatus('connected');
      setLastSeen(new Date());
    } else {
      setConnectionStatus('disconnected');
    }
  }, [isConnected]);

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-yellow-500';
      case 'disconnected':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return `${count} online`;
      case 'connecting':
        return 'Connecting...';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <UsersIcon className="w-4 h-4" />;
      case 'connecting':
        return <WifiIcon className="w-4 h-4 animate-pulse" />;
      case 'disconnected':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      default:
        return <UsersIcon className="w-4 h-4" />;
    }
  };

  return (
    <div className="flex items-center space-x-2 text-sm">
      <div className={`w-2 h-2 rounded-full ${getStatusColor()} ${connectionStatus === 'connecting' ? 'animate-pulse' : ''}`}></div>
      {getStatusIcon()}
      <span className={`${isConnected ? 'text-gray-600' : 'text-red-600'}`}>
        {getStatusText()}
      </span>
      {connectionStatus === 'disconnected' && lastSeen && (
        <span className="text-xs text-gray-400">
          (Last seen: {lastSeen.toLocaleTimeString()})
        </span>
      )}
    </div>
  );
}
