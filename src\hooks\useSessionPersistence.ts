'use client';

import { useEffect, useState, useCallback } from 'react';
import { InterviewFormData } from '@/types/interview';
import { MessageData, VoiceMessageData } from '@/types/chat';

interface SessionData {
  formData: InterviewFormData;
  threadId: string;
  messages: (MessageData | VoiceMessageData)[];
  startTime: number;
  lastActivity: number;
}

interface UseSessionPersistenceReturn {
  sessionData: SessionData | null;
  saveSession: (data: Partial<SessionData>) => void;
  clearSession: () => void;
  restoreSession: () => SessionData | null;
  isSessionExpired: () => boolean;
  updateActivity: () => void;
}

const SESSION_KEY = 'solara_interview_session';
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours

export const useSessionPersistence = (): UseSessionPersistenceReturn => {
  const [sessionData, setSessionData] = useState<SessionData | null>(null);

  // Save session data to localStorage
  const saveSession = useCallback((data: Partial<SessionData>) => {
    try {
      const existingData = localStorage.getItem(SESSION_KEY);
      const existing = existingData ? JSON.parse(existingData) : {};
      
      const updatedData: SessionData = {
        ...existing,
        ...data,
        lastActivity: Date.now()
      };

      localStorage.setItem(SESSION_KEY, JSON.stringify(updatedData));
      setSessionData(updatedData);
    } catch (error) {
      console.error('Failed to save session data:', error);
    }
  }, []);

  // Clear session data
  const clearSession = useCallback(() => {
    try {
      localStorage.removeItem(SESSION_KEY);
      sessionStorage.removeItem('interviewFormData');
      sessionStorage.removeItem('interviewSession');
      sessionStorage.removeItem('interviewCompletionData');
      setSessionData(null);
    } catch (error) {
      console.error('Failed to clear session data:', error);
    }
  }, []);

  // Restore session data from localStorage
  const restoreSession = useCallback((): SessionData | null => {
    try {
      const storedData = localStorage.getItem(SESSION_KEY);
      if (!storedData) return null;

      const data: SessionData = JSON.parse(storedData);
      
      // Check if session is expired
      if (isSessionExpired()) {
        clearSession();
        return null;
      }

      setSessionData(data);
      return data;
    } catch (error) {
      console.error('Failed to restore session data:', error);
      clearSession();
      return null;
    }
  }, [clearSession]);

  // Check if session is expired
  const isSessionExpired = useCallback((): boolean => {
    try {
      const storedData = localStorage.getItem(SESSION_KEY);
      if (!storedData) return true;

      const data: SessionData = JSON.parse(storedData);
      const now = Date.now();
      const timeSinceLastActivity = now - data.lastActivity;

      return timeSinceLastActivity > SESSION_TIMEOUT;
    } catch (error) {
      console.error('Failed to check session expiry:', error);
      return true;
    }
  }, []);

  // Update last activity timestamp
  const updateActivity = useCallback(() => {
    if (sessionData) {
      saveSession({ lastActivity: Date.now() });
    }
  }, [sessionData, saveSession]);

  // Auto-save session data periodically
  useEffect(() => {
    const interval = setInterval(() => {
      if (sessionData) {
        updateActivity();
      }
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [sessionData, updateActivity]);

  // Handle page visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && sessionData) {
        updateActivity();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [sessionData, updateActivity]);

  // Handle beforeunload to save final state
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (sessionData) {
        // Save final state synchronously
        try {
          const finalData = {
            ...sessionData,
            lastActivity: Date.now()
          };
          localStorage.setItem(SESSION_KEY, JSON.stringify(finalData));
        } catch (error) {
          console.error('Failed to save session on unload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [sessionData]);

  // Initialize session on mount
  useEffect(() => {
    const restored = restoreSession();
    if (restored) {
      // Sync with sessionStorage for compatibility
      try {
        if (restored.formData) {
          sessionStorage.setItem('interviewFormData', JSON.stringify(restored.formData));
        }
      } catch (error) {
        console.error('Failed to sync with sessionStorage:', error);
      }
    }
  }, [restoreSession]);

  return {
    sessionData,
    saveSession,
    clearSession,
    restoreSession,
    isSessionExpired,
    updateActivity
  };
};
