'use client';

import { MessageData, VoiceMessageData } from '@/types/chat';
import VoicePlayer from './VoicePlayer';

interface ChatMessageProps {
  message: MessageData | VoiceMessageData;
  isOwnMessage: boolean;
}

export default function ChatMessage({ message, isOwnMessage }: ChatMessageProps) {
  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = () => {
    switch (message.status) {
      case 'sent':
        return <div className="w-2 h-2 bg-gray-400 rounded-full"></div>;
      case 'delivered':
        return <div className="w-2 h-2 bg-blue-500 rounded-full"></div>;
      case 'failed':
        return <div className="w-2 h-2 bg-red-500 rounded-full"></div>;
      default:
        return null;
    }
  };

  return (
    <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-4 ${
      isOwnMessage ? 'animate-slide-in-right' : 'animate-slide-in-left'
    }`}>
      <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
        {/* Username */}
        {!isOwnMessage && (
          <div className="text-xs text-gray-500 mb-1 px-3">
            {message.username}
          </div>
        )}
        
        {/* Message bubble */}
        <div
          className={`px-4 py-2 rounded-lg ${
            isOwnMessage
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-900'
          }`}
        >
          {message.type === 'text' ? (
            <p className="text-sm whitespace-pre-wrap break-words">
              {(message as MessageData).message}
            </p>
          ) : (
            <VoicePlayer
              audioData={(message as VoiceMessageData).audioData}
              duration={(message as VoiceMessageData).duration}
              isOwnMessage={isOwnMessage}
            />
          )}
        </div>
        
        {/* Timestamp and status */}
        <div className={`flex items-center mt-1 px-3 space-x-2 ${
          isOwnMessage ? 'justify-end' : 'justify-start'
        }`}>
          <span className="text-xs text-gray-500">
            {formatTime(message.timestamp)}
          </span>
          {isOwnMessage && getStatusIcon()}
        </div>
      </div>
    </div>
  );
}
