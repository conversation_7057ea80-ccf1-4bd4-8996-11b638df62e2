// Performance monitoring utilities for Solara

export interface PerformanceMetrics {
  messageLatency: number[];
  voiceProcessingTime: number[];
  connectionTime: number;
  renderTime: number[];
  memoryUsage: number[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    messageLatency: [],
    voiceProcessingTime: [],
    connectionTime: 0,
    renderTime: [],
    memoryUsage: []
  };

  private timers: Map<string, number> = new Map();

  // Start timing an operation
  startTimer(operation: string): void {
    this.timers.set(operation, performance.now());
  }

  // End timing and record the duration
  endTimer(operation: string): number {
    const startTime = this.timers.get(operation);
    if (!startTime) {
      console.warn(`Timer for operation "${operation}" was not started`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(operation);

    // Record metrics based on operation type
    switch (operation) {
      case 'message_send':
      case 'message_receive':
        this.metrics.messageLatency.push(duration);
        break;
      case 'voice_processing':
        this.metrics.voiceProcessingTime.push(duration);
        break;
      case 'component_render':
        this.metrics.renderTime.push(duration);
        break;
    }

    return duration;
  }

  // Record connection time
  recordConnectionTime(time: number): void {
    this.metrics.connectionTime = time;
  }

  // Record memory usage (if available)
  recordMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage.push(memory.usedJSHeapSize);
    }
  }

  // Get performance statistics
  getStats(): {
    avgMessageLatency: number;
    avgVoiceProcessingTime: number;
    avgRenderTime: number;
    connectionTime: number;
    currentMemoryUsage: number;
    totalMessages: number;
    totalVoiceMessages: number;
  } {
    const avgMessageLatency = this.metrics.messageLatency.length > 0
      ? this.metrics.messageLatency.reduce((a, b) => a + b, 0) / this.metrics.messageLatency.length
      : 0;

    const avgVoiceProcessingTime = this.metrics.voiceProcessingTime.length > 0
      ? this.metrics.voiceProcessingTime.reduce((a, b) => a + b, 0) / this.metrics.voiceProcessingTime.length
      : 0;

    const avgRenderTime = this.metrics.renderTime.length > 0
      ? this.metrics.renderTime.reduce((a, b) => a + b, 0) / this.metrics.renderTime.length
      : 0;

    const currentMemoryUsage = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
      : 0;

    return {
      avgMessageLatency: Math.round(avgMessageLatency * 100) / 100,
      avgVoiceProcessingTime: Math.round(avgVoiceProcessingTime * 100) / 100,
      avgRenderTime: Math.round(avgRenderTime * 100) / 100,
      connectionTime: Math.round(this.metrics.connectionTime * 100) / 100,
      currentMemoryUsage: Math.round(currentMemoryUsage / 1024 / 1024 * 100) / 100, // MB
      totalMessages: this.metrics.messageLatency.length,
      totalVoiceMessages: this.metrics.voiceProcessingTime.length
    };
  }

  // Clear old metrics to prevent memory leaks
  cleanup(): void {
    const maxEntries = 100;
    
    if (this.metrics.messageLatency.length > maxEntries) {
      this.metrics.messageLatency = this.metrics.messageLatency.slice(-maxEntries);
    }
    
    if (this.metrics.voiceProcessingTime.length > maxEntries) {
      this.metrics.voiceProcessingTime = this.metrics.voiceProcessingTime.slice(-maxEntries);
    }
    
    if (this.metrics.renderTime.length > maxEntries) {
      this.metrics.renderTime = this.metrics.renderTime.slice(-maxEntries);
    }
    
    if (this.metrics.memoryUsage.length > maxEntries) {
      this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-maxEntries);
    }
  }

  // Export metrics for debugging
  exportMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Reset all metrics
  reset(): void {
    this.metrics = {
      messageLatency: [],
      voiceProcessingTime: [],
      connectionTime: 0,
      renderTime: [],
      memoryUsage: []
    };
    this.timers.clear();
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for common performance measurements
export const measureAsync = async <T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> => {
  performanceMonitor.startTimer(operation);
  try {
    const result = await fn();
    performanceMonitor.endTimer(operation);
    return result;
  } catch (error) {
    performanceMonitor.endTimer(operation);
    throw error;
  }
};

export const measureSync = <T>(
  operation: string,
  fn: () => T
): T => {
  performanceMonitor.startTimer(operation);
  try {
    const result = fn();
    performanceMonitor.endTimer(operation);
    return result;
  } catch (error) {
    performanceMonitor.endTimer(operation);
    throw error;
  }
};

// Debounce utility for performance optimization
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle utility for performance optimization
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
